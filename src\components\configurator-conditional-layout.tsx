"use client"

import { SystemTopBar } from "@/components/system-top-bar"
import { SidebarProvider } from "@/components/ui/sidebar"

interface ConfiguratorConditionalLayoutProps {
  children: React.ReactNode
}

export function ConfiguratorConditionalLayout({ children }: ConfiguratorConditionalLayoutProps) {
  return (
    <div className="pt-12"> {/* Add top padding for fixed system bar */}
      <SidebarProvider>
        {/* System Top Bar - Fixed at top */}
        <SystemTopBar />

        {/* Main Content */}
        {children}
      </SidebarProvider>
    </div>
  )
}
