import {
  Activity,
  Archive,
  BarChart3,
  FileText,
  LayoutDashboard,
  Monitor,
  Package,
  Shield,
  ShieldCheck,
  Users,
  Workflow,
  Wrench,
  type LucideIcon,
} from "lucide-react"

export interface SubModule {
  title: string
  url: string
  description?: string
}

export interface NavigationItem {
  title: string
  url: string
  icon?: LucideIcon
  isActive?: boolean
  subModules?: SubModule[]
}

export interface NavigationData {
  user: {
    name: string
    email: string
    avatar: string
  }
  navMain: NavigationItem[]
}

// GRCOS navigation data - Flat, Agent-Orchestrated Architecture
export const navigationData: NavigationData = {
  user: {
    name: "GRC Admin",
    email: "<EMAIL>",
    avatar: "/avatars/admin.jpg",
  },
  // Entity management is now handled by EntitySwitcher component
  // Teams section replaced with multi-entity functionality
  navMain: [
    {
      title: "Overview",
      url: "/overview/executive",
      icon: LayoutDashboard,
      subModules: [
        { title: "Executive Summary", url: "/overview/executive", description: "High-level executive dashboard view" },
        { title: "System Agent Status", url: "/overview/agents", description: "Real-time agent activity and orchestration health" },
        { title: "Risk Posture", url: "/overview/risk", description: "Cross-domain risk posture overview" },
        { title: "Compliance Status", url: "/overview/compliance", description: "Overall compliance status across frameworks" },
      ]
    },
    {
      title: "Activity",
      url: "/activity/events",
      icon: Activity,
      subModules: [
        { title: "Event Stream", url: "/activity/events", description: "Unified event stream from all system components" },
        { title: "Agent Actions", url: "/activity/agents", description: "Agent-initiated actions and automated responses" },
        { title: "Security Events", url: "/activity/security", description: "Security events and compliance activities" },
        { title: "Audit Trail", url: "/activity/audit", description: "Audit trail with blockchain verification links" },
      ]
    },
    {
      title: "Assets",
      url: "/assets/it",
      icon: Package,
      subModules: [
        { title: "IT Assets", url: "/assets/it", description: "Servers, workstations, network infrastructure" },
        { title: "OT Assets", url: "/assets/ot", description: "Industrial control systems, SCADA, manufacturing equipment" },
        { title: "IoT Devices", url: "/assets/iot", description: "Connected sensors, smart devices, edge computing" },
        { title: "Identities", url: "/assets/identities", description: "Users, service accounts, privileged access management" },
        { title: "Applications", url: "/assets/applications", description: "Software inventory, cloud services, SaaS platforms" },
        { title: "Vendors", url: "/assets/vendors", description: "Third-party relationships, supplier risk management" },
        { title: "Processes", url: "/assets/processes", description: "Business workflows, operational procedures, governance processes" },
      ]
    },
    {
      title: "Monitor",
      url: "/monitor/security",
      icon: Monitor,
      subModules: [
        { title: "Security Dashboard", url: "/monitor/security", description: "Real-time security monitoring across all asset categories" },
        { title: "Anomaly Detection", url: "/monitor/anomaly", description: "AI-powered anomaly detection and behavioral analysis" },
        { title: "SIEM Dashboard", url: "/monitor/siem", description: "Unified SIEM dashboard with Wazuh integration" },
        { title: "Threat Correlation", url: "/monitor/correlation", description: "Cross-environment threat correlation (IT/OT/IoT)" },
        { title: "Alert Prioritization", url: "/monitor/alerts", description: "System Agent coordinated alert prioritization" },
      ]
    },
    {
      title: "Frameworks",
      url: "/frameworks/nist-csf",
      icon: Shield,
      subModules: [
        { title: "NIST CSF 2.0", url: "/frameworks/nist-csf", description: "NIST Cybersecurity Framework implementation" },
        { title: "ISO 27001", url: "/frameworks/iso-27001", description: "Information Security Management System" },
        { title: "SOC 2", url: "/frameworks/soc-2", description: "Service Organization Control 2 compliance" },
        { title: "PCI DSS", url: "/frameworks/pci-dss", description: "Payment Card Industry Data Security Standard" },
        { title: "HIPAA", url: "/frameworks/hipaa", description: "Health Insurance Portability and Accountability Act" },
        { title: "OSCAL Modeling", url: "/frameworks/oscal", description: "OSCAL-based framework modeling and management" },
        { title: "Multi-Framework", url: "/frameworks/harmonization", description: "Multi-framework harmonization and control mapping" },
      ]
    },
    {
      title: "Controls",
      url: "/controls/implementation",
      icon: ShieldCheck,
      subModules: [
        { title: "Implementation", url: "/controls/implementation", description: "Security control implementation and tracking" },
        { title: "Testing", url: "/controls/testing", description: "OSCAL standardized control testing" },
        { title: "Assessment", url: "/controls/assessment", description: "Compliance Agent automated control assessment" },
        { title: "Validation", url: "/controls/validation", description: "Control validation and effectiveness measurement" },
        { title: "Mapping", url: "/controls/mapping", description: "Cross-framework control mapping" },
      ]
    },
    {
      title: "Policies",
      url: "/policies/management",
      icon: FileText,
      subModules: [
        { title: "Management", url: "/policies/management", description: "Policy management and enforcement across all environments" },
        { title: "Policy as Code", url: "/policies/code", description: "OPA-based policy-as-code implementation" },
        { title: "Translation", url: "/policies/translation", description: "Compliance Agent policy translation and application" },
        { title: "Consistency", url: "/policies/consistency", description: "Cross-environment policy consistency verification" },
        { title: "Enforcement", url: "/policies/enforcement", description: "OPA policy enforcement integration" },
      ]
    },
    {
      title: "Assessments",
      url: "/assessments/risk",
      icon: BarChart3,
      subModules: [
        { title: "Risk Assessments", url: "/assessments/risk", description: "Automated risk assessments and gap analysis" },
        { title: "Control Testing", url: "/assessments/testing", description: "Assessment Agent orchestrated control testing" },
        { title: "Gap Analysis", url: "/assessments/gaps", description: "Compliance gap identification and analysis" },
        { title: "Quantitative Risk", url: "/assessments/quantitative", description: "Quantitative risk analysis with Open Source Risk Engine" },
        { title: "Continuous Assessment", url: "/assessments/continuous", description: "Continuous assessment scheduling and execution" },
      ]
    },
    {
      title: "Workflows",
      url: "/workflows/automation",
      icon: Workflow,
      subModules: [
        { title: "Process Automation", url: "/workflows/automation", description: "Business process automation with Flowable engine" },
        { title: "Optimization", url: "/workflows/optimization", description: "Workflow Agent orchestrated process optimization" },
        { title: "Design", url: "/workflows/design", description: "Custom workflow design and template management" },
        { title: "Integration", url: "/workflows/integration", description: "Integration with compliance and security processes" },
        { title: "Analytics", url: "/workflows/analytics", description: "Workflow performance analytics" },
      ]
    },
    {
      title: "Remediation",
      url: "/remediation/incidents",
      icon: Wrench,
      subModules: [
        { title: "Incident Response", url: "/remediation/incidents", description: "Incident response and security remediation coordination" },
        { title: "Automated Response", url: "/remediation/automated", description: "Remediation Agent automated response orchestration" },
        { title: "DFIR Integration", url: "/remediation/dfir", description: "DFIR-IRIS integration for structured investigation" },
        { title: "Cross-Environment", url: "/remediation/cross-env", description: "Cross-environment remediation tracking and validation" },
        { title: "Validation", url: "/remediation/validation", description: "Remediation validation and testing" },
      ]
    },
    {
      title: "Reports",
      url: "/reports/compliance",
      icon: FileText,
      subModules: [
        { title: "Compliance Documentation", url: "/reports/compliance", description: "Reporting Agent generated compliance documentation" },
        { title: "Interactive Dashboards", url: "/reports/dashboards", description: "Interactive dashboards and static report artifacts" },
        { title: "Executive Summaries", url: "/reports/executive", description: "Executive summaries and regulatory submission preparation" },
        { title: "Real-time Status", url: "/reports/realtime", description: "Real-time compliance status and trend analysis" },
        { title: "Audit Reports", url: "/reports/audit", description: "Audit preparation and reports" },
      ]
    },
    {
      title: "Artifacts",
      url: "/artifacts/evidence",
      icon: Archive,
      subModules: [
        { title: "Evidence Repository", url: "/artifacts/evidence", description: "Blockchain-secured evidence repository" },
        { title: "Documentation Storage", url: "/artifacts/documentation", description: "Immutable compliance documentation storage" },
        { title: "Verification", url: "/artifacts/verification", description: "Cryptographic verification of all security artifacts" },
        { title: "Audit-Ready Evidence", url: "/artifacts/audit", description: "Audit-ready evidence with tamper-proof integrity" },
        { title: "Archive Management", url: "/artifacts/archive", description: "Evidence archival and retention management" },
      ]
    },
    {
      title: "Portals",
      url: "/portals/trust",
      icon: Users,
      subModules: [
        { title: "Trust Portals", url: "/portals/trust", description: "Stakeholder-specific trust portals and interfaces" },
        { title: "AI Chatbots", url: "/portals/chatbots", description: "AI-powered chatbots for audit facilitation" },
        { title: "Self-Service", url: "/portals/self-service", description: "Self-service compliance questionnaire completion" },
        { title: "Stakeholder Communication", url: "/portals/communication", description: "Secure stakeholder communication and evidence sharing" },
        { title: "Portal Management", url: "/portals/management", description: "Portal configuration and access management" },
      ]
    },
  ],
}
