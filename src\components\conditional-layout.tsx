"use client"

import { usePathname } from "next/navigation"
import { AppSidebar } from "@/components/app-sidebar"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { SystemTopBar } from "@/components/system-top-bar"
import { SubNavigation } from "@/components/top-navigation"
import { ConfiguratorConditionalLayout } from "@/components/configurator-conditional-layout"

interface ConditionalLayoutProps {
  children: React.ReactNode
}

export function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname()

  // Pages that should not have the sidebar layout
  const excludedPaths = ["/login"]

  // Pages that should use the configurator layout (no sidebar, but with system top bar)
  const configuratorPaths = ["/portals/configure"]

  const shouldExcludeSidebar = excludedPaths.includes(pathname)
  const shouldUseConfiguratorLayout = configuratorPaths.some(path => pathname.startsWith(path))

  if (shouldExcludeSidebar) {
    return <>{children}</>
  }

  if (shouldUseConfiguratorLayout) {
    return (
      <ConfiguratorConditionalLayout>
        {children}
      </ConfiguratorConditionalLayout>
    )
  }

  return (
    <div className="pt-12"> {/* Add top padding for fixed system bar */}
      <SidebarProvider>
        {/* System Top Bar - Fixed at top */}
        <SystemTopBar />

        {/* Main Layout with Sidebar */}
        <AppSidebar />
        <SidebarInset>
          {/* Sub-Navigation Bar */}
          <header className="flex h-12 shrink-0 items-center justify-center border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="flex items-center justify-center px-4 min-w-0">
              <SubNavigation />
            </div>
          </header>

          {/* Main Content */}
          <div className="flex flex-1 flex-col gap-4 p-4">
            {children}
          </div>
        </SidebarInset>
      </SidebarProvider>
    </div>
  )
}
