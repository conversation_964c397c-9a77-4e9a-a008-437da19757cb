"use client"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { 
  ArrowLeft, 
  Save, 
  Eye, 
  Rocket, 
  CheckCircle, 
  AlertTriangle,
  Clock
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"

export type ConfigSection = 
  | 'general'
  | 'access'
  | 'content'
  | 'dashboard'
  | 'chatbot'
  | 'api'
  | 'questionnaire'
  | 'stakeholders'

interface ConfiguratorLayoutProps {
  children: React.ReactNode
  portalId: string
  portalName?: string
  currentSection: ConfigSection
  onSectionChange: (section: ConfigSection) => void
  isDraft?: boolean
  hasUnsavedChanges?: boolean
  validationErrors?: string[]
  onSave?: () => void
  onPreview?: () => void
  onDeploy?: () => void
}

const configSections: Array<{
  id: ConfigSection
  label: string
  description: string
}> = [
  { id: 'general', label: 'General Settings', description: 'Basic portal configuration' },
  { id: 'access', label: 'Access & Security', description: 'Authentication and permissions' },
  { id: 'content', label: 'Content & Data', description: 'Frameworks and data exposure' },
  { id: 'dashboard', label: 'Dashboard Design', description: 'Layout and customization' },
  { id: 'chatbot', label: 'Chatbot Configuration', description: 'AI assistant settings' },
  { id: 'api', label: 'API Settings', description: 'API endpoints and access' },
  { id: 'questionnaire', label: 'Questionnaire Automation', description: 'Self-service forms' },
  { id: 'stakeholders', label: 'Stakeholder Management', description: 'User access and roles' },
]

export function ConfiguratorLayout({
  children,
  portalId,
  portalName = "New Portal",
  currentSection,
  onSectionChange,
  isDraft = true,
  hasUnsavedChanges = false,
  validationErrors = [],
  onSave,
  onPreview,
  onDeploy
}: ConfiguratorLayoutProps) {
  const router = useRouter()
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  const handleSave = async () => {
    if (onSave) {
      await onSave()
      setLastSaved(new Date())
    }
  }

  const canDeploy = validationErrors.length === 0 && !hasUnsavedChanges

  return (
    <div className="min-h-full bg-background">
      {/* Configurator Header - positioned within sidebar layout */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-12 z-40 -mx-4 px-4">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3 py-3 lg:h-14 lg:py-0">
          {/* Left side - Back button and breadcrumb */}
          <div className="flex items-center gap-2 lg:gap-4 min-w-0 flex-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/portals')}
              className="gap-2 shrink-0"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="hidden sm:inline">Back to Dashboard</span>
              <span className="sm:hidden">Back</span>
            </Button>
            <Separator orientation="vertical" className="h-6 hidden sm:block" />
            <div className="min-w-0 flex-1">
              <h1 className="font-semibold text-sm lg:text-base truncate">{portalName}</h1>
              <div className="flex items-center gap-2 text-xs text-muted-foreground flex-wrap">
                <span className="hidden sm:inline">Portal Configuration</span>
                <span className="sm:hidden">Config</span>
                {isDraft && (
                  <Badge variant="secondary" className="text-xs">
                    Draft
                  </Badge>
                )}
                {hasUnsavedChanges && (
                  <Badge variant="warning" className="text-xs">
                    Unsaved
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Right side - Actions */}
          <div className="flex items-center gap-2 shrink-0">
            {lastSaved && (
              <div className="hidden lg:flex items-center gap-1 text-xs text-muted-foreground mr-4">
                <Clock className="h-3 w-3" />
                <span>Saved {lastSaved.toLocaleTimeString()}</span>
              </div>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={onPreview}
              className="gap-2"
            >
              <Eye className="h-4 w-4" />
              <span className="hidden sm:inline">Preview</span>
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleSave}
              disabled={!hasUnsavedChanges}
              className="gap-2"
            >
              <Save className="h-4 w-4" />
              <span className="hidden sm:inline">Save</span>
            </Button>

            <Button
              size="sm"
              onClick={onDeploy}
              disabled={!canDeploy}
              className="gap-2"
            >
              <Rocket className="h-4 w-4" />
              <span className="hidden sm:inline">{isDraft ? 'Deploy' : 'Update'}</span>
            </Button>
          </div>
        </div>

        {/* Section Navigation */}
        <div className="flex items-center gap-1 pb-3 overflow-x-auto scrollbar-hide">
          {configSections.map((section, index) => {
            const isActive = currentSection === section.id
            const isCompleted = false // TODO: Add completion logic

            return (
              <Button
                key={section.id}
                variant={isActive ? "default" : "ghost"}
                size="sm"
                onClick={() => onSectionChange(section.id)}
                className={cn(
                  "flex items-center gap-1 lg:gap-2 whitespace-nowrap shrink-0 text-xs lg:text-sm",
                  isActive && "bg-primary text-primary-foreground"
                )}
              >
                {isCompleted ? (
                  <CheckCircle className="h-3 w-3" />
                ) : validationErrors.some(error => error.includes(section.id)) ? (
                  <AlertTriangle className="h-3 w-3" />
                ) : (
                  <span className="flex h-3 w-3 items-center justify-center text-xs font-medium">
                    {index + 1}
                  </span>
                )}
                <span className="hidden sm:inline">{section.label}</span>
                <span className="sm:hidden">{section.label.split(' ')[0]}</span>
              </Button>
            )
          })}
        </div>
      </div>

      {/* Main Content - adjusted for sidebar layout */}
      <div className="py-4 lg:py-6">
        <div className="w-full max-w-none lg:max-w-6xl lg:mx-auto">
          {/* Section Header */}
          <div className="mb-4 lg:mb-6">
            <h2 className="text-xl lg:text-2xl font-bold tracking-tight">
              {configSections.find(s => s.id === currentSection)?.label}
            </h2>
            <p className="text-sm lg:text-base text-muted-foreground mt-1">
              {configSections.find(s => s.id === currentSection)?.description}
            </p>
          </div>

          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <div className="mb-4 lg:mb-6 p-3 lg:p-4 border border-destructive/20 bg-destructive/5 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle className="h-4 w-4 text-destructive" />
                <h3 className="font-medium text-destructive text-sm lg:text-base">Configuration Issues</h3>
              </div>
              <ul className="text-xs lg:text-sm text-destructive space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Section Content */}
          <div className="space-y-4 lg:space-y-6">
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}
